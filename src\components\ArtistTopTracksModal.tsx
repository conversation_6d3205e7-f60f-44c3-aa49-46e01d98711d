import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { SpotifyArtist, SpotifyTrack, SpotifyPaging, SpotifyService, spotifyService, TimeRange } from '@/services/spotify';
import { Clock, AlertTriangle, HelpCircle } from 'lucide-react';
import { useArtistTopTracks } from '@/hooks/use-spotify-data';
import { formatDuration } from '@/utils/formatters';
import RankingExplanationModal from './RankingExplanationModal';

interface ArtistTopTracksModalProps {
  isOpen: boolean;
  onClose: () => void;
  artist: SpotifyArtist | null;
  timeRange?: TimeRange;
}

// Create a fallback empty data structure
const emptyData: SpotifyPaging<SpotifyTrack> = {
  items: [],
  total: 0,
  limit: 0,
  href: '',
  next: null,
  offset: 0,
  previous: null
};

// Fallback function to format track duration in case the SpotifyService method fails
const formatTrackDuration = (ms: number): string => {
  try {
    // Use our utility function from formatters.ts
    return formatDuration(ms);
  } catch (error) {
    console.error('Error using formatDuration utility:', error);

    // Try to use the static method as a fallback
    try {
      return SpotifyService.formatDuration(ms);
    } catch (staticError) {
      console.error('Error using SpotifyService.formatDuration:', staticError);

      // Last resort fallback implementation
      try {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
      } catch (fallbackError) {
        console.error('Error in fallback duration formatter:', fallbackError);
        return '0:00';
      }
    }
  }
};

const ArtistTopTracksModal: React.FC<ArtistTopTracksModalProps> = ({
  isOpen,
  onClose,
  artist,
  timeRange = 'medium_term',
}) => {
  const [error, setError] = useState<string | null>(null);
  const [artistData, setArtistData] = useState<SpotifyArtist | null>(null);
  const [isExplanationOpen, setIsExplanationOpen] = useState<boolean>(false);

  // Add error logging to help debug issues
  useEffect(() => {
    if (isOpen && artist) {
      try {
        console.log('Opening modal for artist:', {
          id: artist.id || 'missing-id',
          name: artist.name || 'missing-name',
          hasImages: Array.isArray(artist.images) && artist.images.length > 0,
          hasGenres: Array.isArray(artist.genres) && artist.genres.length > 0,
          fullArtist: JSON.stringify(artist)
        });

        // Create a sanitized copy of the artist data with fallbacks for all properties
        const sanitizedArtist: SpotifyArtist = {
          id: artist.id || `unknown-${Date.now()}`,
          name: artist.name || 'Unknown Artist',
          images: Array.isArray(artist.images) ? artist.images : [],
          genres: Array.isArray(artist.genres) ? artist.genres : [],
          popularity: typeof artist.popularity === 'number' ? artist.popularity : 0,
          external_urls: artist.external_urls || { spotify: '' }
        };

        setArtistData(sanitizedArtist);
        setError(null);
      } catch (err) {
        console.error('Error processing artist data:', err);
        setError('Error processing artist data');
      }
    }
  }, [isOpen, artist]);

  // Use our custom hook to fetch artist top tracks
  const { data = emptyData, isLoading, isError } = useArtistTopTracks(
    artistData?.id,
    5,
    timeRange
  );

  // If no artist is provided or modal is not open, don't render anything
  if (!isOpen) return null;

  // Handle error state
  if (error || !artistData) {
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-md w-[95%]">
          <DialogHeader>
            <DialogTitle className="text-center">Error</DialogTitle>
            <DialogDescription className="text-center text-spotify-off-white text-sm">
              Unable to load artist data
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-spotify-off-white">
              {error || "There was an error loading the artist data."}
            </p>
            <button
              onClick={onClose}
              className="mt-4 px-4 py-2 bg-spotify-green text-black rounded-full font-bold"
            >
              Close
            </button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  try {
    // Safely access artist properties with fallbacks
    const artistName = artistData.name || 'Unknown Artist';
    const artistImage = Array.isArray(artistData.images) && artistData.images.length > 0
      ? artistData.images[0]?.url
      : '';
    const topGenre = Array.isArray(artistData.genres) && artistData.genres.length > 0
      ? artistData.genres[0]
      : 'Unknown';

    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-md w-[95%]">
          <DialogHeader>
            <DialogTitle className="text-center">{artistName}</DialogTitle>
            <DialogDescription className="text-center text-spotify-off-white text-sm">
              your most played tracks with this artist
            </DialogDescription>
            <div className="text-center text-spotify-off-white text-xs mt-1">
              from your entire listening history
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsExplanationOpen(true);
                }}
                className="inline-flex items-center ml-2 text-spotify-green hover:underline hover:text-spotify-green/80 transition-colors"
              >
                <HelpCircle size={12} className="mr-1" />
                how are these ranked?
              </button>
            </div>
          </DialogHeader>

          <div className="flex items-center space-x-4 mb-4">
            <img
              src={artistImage || ''}
              alt={artistName}
              className="w-16 h-16 object-cover rounded-full"
            />
            <div>
              <div className="text-sm text-spotify-off-white">Top Genre</div>
              <div className="text-lg font-medium">{topGenre}</div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-t-spotify-green"></div>
            </div>
          ) : isError ? (
            <div className="text-center py-8 text-spotify-off-white">
              <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              Error loading tracks. Please try again.
            </div>
          ) : data?.items && data.items.length > 0 ? (
            <div className="rounded-lg overflow-hidden">
              <div className="max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-spotify-green scrollbar-track-spotify-light-gray">
                <table className="w-full">
                  <thead className="border-b border-spotify-light-gray sticky top-0 bg-spotify-dark-gray z-10">
                    <tr className="text-left text-spotify-off-white text-sm">
                      <th className="px-4 py-2 w-8">#</th>
                      <th className="px-4 py-2">title</th>
                      <th className="px-4 py-2 text-right">
                        <Clock size={16} />
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.items.map((track, index) => {
                      try {
                        // Safely access track properties
                        const trackName = track?.name || 'Unknown Track';
                        const albumName = track?.album?.name || 'Unknown Album';
                        const albumImage = track?.album?.images && track.album.images.length > 0
                          ? track.album.images[0]?.url
                          : '';

                        return (
                          <tr
                            key={track?.id || `track-${index}`}
                            className="hover:bg-spotify-light-gray group"
                          >
                            <td className="px-4 py-3 text-spotify-off-white">{index + 1}</td>
                            <td className="px-4 py-3">
                              <div className="flex items-center">
                                <img
                                  src={albumImage || ''}
                                  alt={trackName}
                                  className="w-10 h-10 mr-3 rounded"
                                />
                                <div>
                                  <div className="font-medium group-hover:text-spotify-green transition-colors">
                                    {trackName}
                                  </div>
                                  <div className="text-xs text-spotify-off-white">
                                    {albumName}
                                    {track._timeRanges && track._timeRanges.length > 0 && (
                                      <span className="ml-2 text-xs text-spotify-green">
                                        {track._timeRanges.includes('short_term') && '4w '}
                                        {track._timeRanges.includes('medium_term') && '6m '}
                                        {track._timeRanges.includes('long_term') && 'all '}
                                        {track._timeRanges.includes('spotify_top') && 'top '}
                                        {track._timeRanges.includes('album_tracks') && 'album '}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 text-right text-spotify-off-white">
                              {typeof track?.duration_ms === 'number'
                                ? formatTrackDuration(track.duration_ms)
                                : '0:00'}
                            </td>
                          </tr>
                        );
                      } catch (err) {
                        console.error('Error rendering track row:', err);
                        return (
                          <tr key={`error-${index}`}>
                            <td colSpan={3} className="px-4 py-3 text-red-400 text-center">
                              Error displaying track
                            </td>
                          </tr>
                        );
                      }
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-spotify-off-white">
              <p>due to Spotify's song tracking, no tracks in your listening history in the short, medium, or long term were found for this artist.</p>
            </div>
          )}

          {/* Ranking Explanation Modal */}
          <RankingExplanationModal
            isOpen={isExplanationOpen}
            onClose={() => setIsExplanationOpen(false)}
            timeRange={timeRange}
          />
        </DialogContent>
      </Dialog>
    );
  } catch (err) {
    console.error('Error rendering modal content:', err);
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-md w-[95%]">
          <DialogHeader>
            <DialogTitle className="text-center">Error</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-spotify-off-white">
              There was an error displaying the artist's top tracks.
            </p>
            <button
              onClick={onClose}
              className="mt-4 px-4 py-2 bg-spotify-green text-black rounded-full font-bold"
            >
              Close
            </button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }
};

export default ArtistTopTracksModal;
